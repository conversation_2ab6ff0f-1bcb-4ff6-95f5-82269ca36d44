# PlaceholderAPI Integration

The OfflineItemDelivery plugin provides PlaceholderAPI integration to display information about pending items for players.

## Setup

1. Install [PlaceholderAPI](https://www.spigotmc.org/resources/placeholderapi.6245/) on your server
2. The OfflineItemDelivery plugin will automatically detect and enable PlaceholderAPI integration
3. No additional configuration is required

## Available Placeholders

All placeholders use the identifier `offlineitemdelivery` or `oid` for short.

### Basic Placeholders

| Placeholder | Description | Example Output |
|-------------|-------------|----------------|
| `%offlineitemdelivery_pending_count%` | Number of pending items for the player | `5` |
| `%offlineitemdelivery_total_claimable%` | Same as pending_count (alias) | `5` |
| `%offlineitemdelivery_has_pending%` | Whether player has any pending items | `true` or `false` |
| `%offlineitemdelivery_max_pending%` | Maximum allowed pending items (from config) | `50` |
| `%offlineitemdelivery_pending_percentage%` | Percentage of pending items vs max allowed | `10` (for 5/50) |

## Usage Examples

### In Chat Messages
```yaml
# In your chat plugin configuration
format: "&7[%offlineitemdelivery_pending_count%] &f%player_name%: %message%"
```

### In Scoreboard
```yaml
# In your scoreboard plugin
lines:
  - "&ePending Items: &f%offlineitemdelivery_pending_count%"
  - "&eMax Items: &f%offlineitemdelivery_max_pending%"
```

### In Tab List
```yaml
# In your tab plugin
header:
  - "&6Welcome to the server!"
  - "&eYou have &f%offlineitemdelivery_pending_count% &epending items"
```

### Conditional Display
```yaml
# Using conditional placeholders (requires additional plugins like ConditionalPlaceholders)
# Show message only if player has pending items
text: "%offlineitemdelivery_has_pending_true:&eYou have pending items! Use /claimitems%"
```

## Integration with Other Plugins

### Essentials
You can use these placeholders in Essentials MOTD, welcome messages, etc.

### DeluxeMenus
Create custom menus that show pending item counts:
```yaml
gui_settings:
  menu_title: "&bPlayer Stats"
items:
  pending_items:
    material: CHEST
    display_name: "&ePending Items"
    lore:
      - "&7You have &f%offlineitemdelivery_pending_count% &7pending items"
      - "&7Click to claim them!"
    left_click_commands:
      - "[PLAYER] claimitems"
```

### HolographicDisplays
Create holograms showing server-wide statistics:
```yaml
# Note: This would require additional setup for server-wide stats
lines:
  - "&6Server Statistics"
  - "&eYour pending items: &f%offlineitemdelivery_pending_count%"
```

## Notes

- Placeholders return `0` or `false` if there's a database error
- The plugin must be enabled and database connected for placeholders to work
- Placeholders are updated in real-time when items are added or claimed
- All placeholders work for both online and offline players (when supported by the requesting plugin)

## Troubleshooting

If placeholders aren't working:

1. Ensure PlaceholderAPI is installed and enabled
2. Check that OfflineItemDelivery is properly loaded after PlaceholderAPI
3. Verify the database connection is working
4. Check the server console for any error messages during plugin startup
5. Use `/papi parse <player> %offlineitemdelivery_pending_count%` to test placeholders directly
