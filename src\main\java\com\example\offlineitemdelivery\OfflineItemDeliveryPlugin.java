package com.example.offlineitemdelivery;

import com.example.offlineitemdelivery.commands.ClaimItemsCommand;
import com.example.offlineitemdelivery.commands.GiveOfflineCommand;
import com.example.offlineitemdelivery.commands.OfflineQueueCommand;
import com.example.offlineitemdelivery.database.DatabaseManager;
import com.example.offlineitemdelivery.gui.ClaimItemsGUI;
import com.example.offlineitemdelivery.integrations.ItemsAdderIntegration;
import com.example.offlineitemdelivery.integrations.PlaceholderAPIExpansion;
import com.example.offlineitemdelivery.listeners.PlayerJoinListener;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.ChatColor;
import org.bukkit.command.CommandSender;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.logging.Level;

public class OfflineItemDeliveryPlugin extends JavaPlugin {
    
    private DatabaseManager databaseManager;
    private ItemDeliveryService itemDeliveryService;
    private ClaimItemsGUI claimItemsGUI;
    private PlaceholderAPIExpansion placeholderExpansion;
    
    @Override
    public void onEnable() {
        // Save default config
        saveDefaultConfig();

        // Initialize ItemsAdder integration
        ItemsAdderIntegration.initialize();

        // Initialize database
        try {
            databaseManager = new DatabaseManager(this);
            databaseManager.initialize();
            getLogger().info("Database connection established successfully!");
        } catch (Exception e) {
            getLogger().log(Level.SEVERE, "Failed to initialize database connection!", e);
            getServer().getPluginManager().disablePlugin(this);
            return;
        }
        
        // Initialize services
        itemDeliveryService = new ItemDeliveryService(this, databaseManager);
        claimItemsGUI = new ClaimItemsGUI(this, itemDeliveryService);

        // Register listeners
        getServer().getPluginManager().registerEvents(new PlayerJoinListener(this, itemDeliveryService), this);
        getServer().getPluginManager().registerEvents(claimItemsGUI, this);
        
        // Register commands
        getCommand("giveoffline").setExecutor(new GiveOfflineCommand(this, itemDeliveryService));
        getCommand("offlinequeue").setExecutor(new OfflineQueueCommand(this, itemDeliveryService));
        getCommand("claimitems").setExecutor(new ClaimItemsCommand(this));

        // Initialize PlaceholderAPI integration if available
        initializePlaceholderAPI();

        getLogger().info("OfflineItemDelivery plugin has been enabled!");
    }
    
    @Override
    public void onDisable() {
        // Close database connections
        if (databaseManager != null) {
            databaseManager.close();
            getLogger().info("Database connections closed.");
        }
        
        getLogger().info("OfflineItemDelivery plugin has been disabled!");
    }
    
    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }
    
    public ItemDeliveryService getItemDeliveryService() {
        return itemDeliveryService;
    }
    
    public String getServerId() {
        return getConfig().getString("server.id", "default");
    }

    public ClaimItemsGUI getClaimItemsGUI() {
        return claimItemsGUI;
    }

    /**
     * Get the formatted message prefix
     * @return The formatted prefix with color codes translated
     */
    public String getPrefix() {
        String prefix = getConfig().getString("settings.prefix", "&6[&bRedeemItems&6] ");
        return ChatColor.translateAlternateColorCodes('&', prefix);
    }

    /**
     * Send a message to a CommandSender with the plugin prefix
     * @param sender The CommandSender to send the message to
     * @param message The message to send (color codes will be translated)
     */
    public void sendMessage(CommandSender sender, String message) {
        String formattedMessage = getPrefix() + ChatColor.translateAlternateColorCodes('&', message);
        sender.sendMessage(formattedMessage);
    }

    /**
     * Format a message with the plugin prefix
     * @param message The message to format (color codes will be translated)
     * @return The formatted message with prefix
     */
    public String formatMessage(String message) {
        return getPrefix() + ChatColor.translateAlternateColorCodes('&', message);
    }

    /**
     * Initialize PlaceholderAPI integration if the plugin is available
     */
    private void initializePlaceholderAPI() {
        if (getServer().getPluginManager().getPlugin("PlaceholderAPI") != null) {
            try {
                placeholderExpansion = new PlaceholderAPIExpansion(this, itemDeliveryService);
                if (placeholderExpansion.register()) {
                    getLogger().info("PlaceholderAPI integration enabled!");
                } else {
                    getLogger().warning("Failed to register PlaceholderAPI expansion!");
                }
            } catch (Exception e) {
                getLogger().warning("Error initializing PlaceholderAPI integration: " + e.getMessage());
            }
        } else {
            getLogger().info("PlaceholderAPI not found - placeholder integration disabled");
        }
    }
}
