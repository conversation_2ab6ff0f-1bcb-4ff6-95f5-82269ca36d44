package com.example.offlineitemdelivery.commands;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class ClaimItemsCommand implements CommandExecutor {

    private final OfflineItemDeliveryPlugin plugin;

    public ClaimItemsCommand(OfflineItemDeliveryPlugin plugin) {
        this.plugin = plugin;
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        // Only players can claim items
        if (!(sender instanceof Player)) {
            plugin.sendMessage(sender, "&cOnly players can use this command.");
            return true;
        }

        Player player = (Player) sender;

        // Check permission
        if (!player.hasPermission("offlineitemdelivery.claim")) {
            plugin.sendMessage(player, "&cYou don't have permission to use this command.");
            return true;
        }

        // Open the GUI for all cases (no more subcommands)
        plugin.getClaimItemsGUI().openClaimGUI(player);
        return true;
    }
}
