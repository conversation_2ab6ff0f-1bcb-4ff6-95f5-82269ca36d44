package com.example.offlineitemdelivery.commands;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.integrations.ItemsAdderIntegration;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.OfflinePlayer;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

public class GiveOfflineCommand implements CommandExecutor, TabCompleter {
    
    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;
    
    public GiveOfflineCommand(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("offlineitemdelivery.give")) {
            plugin.sendMessage(sender, "&cYou don't have permission to use this command.");
            return true;
        }
        
        if (args.length < 2) {
            plugin.sendMessage(sender, "&cUsage: /" + label + " <player> <item> [amount] [-server <servername>] [-ia]");
            plugin.sendMessage(sender, "&7Examples:");
            plugin.sendMessage(sender, "&7  /" + label + " Steve diamond_sword 1");
            plugin.sendMessage(sender, "&7  /" + label + " Alex apple 64 -server survival");
            plugin.sendMessage(sender, "&7  /" + label + " Bob hand (gives item in your hand)");
            plugin.sendMessage(sender, "&7  /" + label + " Alice myitems:custom_sword 1 -ia");
            plugin.sendMessage(sender, "&7  /" + label + " Charlie myitems:magic_wand 1 -ia -server creative");
            return true;
        }
        
        String targetPlayerName = args[0];
        String itemName = args[1];
        int amount = 1;
        String targetServer = null;
        boolean useItemsAdder = false;

        // Parse arguments (amount, server parameter, and ItemsAdder flag)
        for (int i = 2; i < args.length; i++) {
            if (args[i].equalsIgnoreCase("-server") && i + 1 < args.length) {
                targetServer = args[i + 1];
                i++; // Skip the next argument as it's the server name
            } else if (args[i].equalsIgnoreCase("-ia") || args[i].equalsIgnoreCase("-itemsadder")) {
                useItemsAdder = true;
            } else {
                // Try to parse as amount if we haven't set it yet and it's a number
                if (amount == 1) {
                    try {
                        int parsedAmount = Integer.parseInt(args[i]);
                        if (parsedAmount <= 0) {
                            plugin.sendMessage(sender, "&cAmount must be greater than 0.");
                            return true;
                        }
                        if (parsedAmount > 2304) { // 36 stacks of 64
                            plugin.sendMessage(sender, "&cAmount is too large. Maximum is 2304.");
                            return true;
                        }
                        amount = parsedAmount;
                    } catch (NumberFormatException e) {
                        plugin.sendMessage(sender, "&cInvalid argument: " + args[i]);
                        plugin.sendMessage(sender, "&7Use -server <servername> to specify a target server");
                        plugin.sendMessage(sender, "&7Use -ia or -itemsadder for ItemsAdder items");
                        return true;
                    }
                }
            }
        }
        
        // Get target player
        OfflinePlayer targetPlayer = Bukkit.getOfflinePlayer(targetPlayerName);
        if (targetPlayer == null || (!targetPlayer.hasPlayedBefore() && !targetPlayer.isOnline())) {
            plugin.sendMessage(sender, "&cPlayer '" + targetPlayerName + "' has never played on this server.");
            return true;
        }
        
        // Get item to give
        ItemStack itemToGive;
        if (itemName.equalsIgnoreCase("hand")) {
            if (!(sender instanceof Player)) {
                plugin.sendMessage(sender, "&cOnly players can use 'hand' as an item.");
                return true;
            }

            Player senderPlayer = (Player) sender;
            ItemStack handItem = senderPlayer.getInventory().getItemInMainHand();

            if (handItem == null || handItem.getType() == Material.AIR) {
                plugin.sendMessage(sender, "&cYou must be holding an item to use 'hand'.");
                return true;
            }

            itemToGive = handItem.clone();
        } else if (useItemsAdder) {
            // Handle ItemsAdder items
            if (!ItemsAdderIntegration.isEnabled()) {
                plugin.sendMessage(sender, "&cItemsAdder is not installed or enabled on this server.");
                return true;
            }

            ItemStack customItem = ItemsAdderIntegration.getCustomItem(itemName);
            if (customItem == null) {
                plugin.sendMessage(sender, "&cItemsAdder item not found: " + itemName);
                plugin.sendMessage(sender, "&7Make sure the item ID is correct (e.g., 'myitems:custom_sword')");
                return true;
            }

            itemToGive = customItem.clone();
        } else {
            // Parse vanilla material
            Material material;
            try {
                material = Material.valueOf(itemName.toUpperCase());
            } catch (IllegalArgumentException e) {
                plugin.sendMessage(sender, "&cInvalid item: " + itemName);
                plugin.sendMessage(sender, "&7Use -ia flag for ItemsAdder items");
                return true;
            }

            itemToGive = new ItemStack(material, 1);
        }
        
        // Always queue items - never give directly to online players
        // This ensures all items are delivered through the GUI system
        
        // Queue the item for offline delivery
        UUID targetUuid = targetPlayer.getUniqueId();
        boolean success = itemDeliveryService.queueItemForPlayer(targetUuid, targetPlayer.getName(), itemToGive, amount, targetServer);

        if (success) {
            String serverInfo = targetServer != null ? " (server: " + targetServer + ")" : "";
            plugin.sendMessage(sender, "&aSuccessfully queued " + amount + "x " +
                getItemDisplayName(itemToGive) + " for " + targetPlayer.getName() + serverInfo + ".");

            if (targetServer != null) {
                plugin.sendMessage(sender, "&7The item can be claimed using &f/claimitems&7 when they join the " +
                    "&e" + targetServer + "&7 server.");
            } else {
                plugin.sendMessage(sender, "&7The item can be claimed using &f/claimitems&7 on any server.");
            }

            // Notify online player if they're online
            if (targetPlayer.isOnline()) {
                Player onlinePlayer = targetPlayer.getPlayer();
                plugin.sendMessage(onlinePlayer, "&eYou have received a new item! Use &f/claimitems&e to claim it.");
            }
        } else {
            plugin.sendMessage(sender, "&cFailed to queue item. The player may have reached their pending item limit.");
        }
        
        return true;
    }
    
    private String getItemDisplayName(ItemStack item) {
        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            return item.getItemMeta().getDisplayName();
        }
        return item.getType().name().toLowerCase().replace('_', ' ');
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        List<String> completions = new ArrayList<>();

        if (args.length == 1) {
            // Player names
            String partial = args[0].toLowerCase();
            for (OfflinePlayer player : Bukkit.getOfflinePlayers()) {
                if (player.getName() != null && player.getName().toLowerCase().startsWith(partial)) {
                    completions.add(player.getName());
                }
            }
        } else if (args.length == 2) {
            // Item names
            String partial = args[1].toLowerCase();
            completions.add("hand");

            // Add vanilla materials
            for (Material material : Material.values()) {
                if (material.isItem() && material.name().toLowerCase().startsWith(partial)) {
                    completions.add(material.name().toLowerCase());
                }
            }

            // Add ItemsAdder items if available
            if (ItemsAdderIntegration.isEnabled()) {
                completions.addAll(ItemsAdderIntegration.getCustomItemIds(partial));
            }
        } else if (args.length >= 3) {
            // Check if the last argument is -server, then suggest server names
            if (args.length >= 2 && args[args.length - 2].equalsIgnoreCase("-server")) {
                // Get server suggestions from config
                String partial = args[args.length - 1].toLowerCase();
                List<String> serverSuggestions = plugin.getConfig().getStringList("server.known-servers");

                // If no servers configured, use defaults
                if (serverSuggestions.isEmpty()) {
                    serverSuggestions = Arrays.asList("survival", "creative", "skyblock", "factions", "prison", "hub");
                }

                for (String serverName : serverSuggestions) {
                    if (serverName.toLowerCase().startsWith(partial)) {
                        completions.add(serverName);
                    }
                }
            } else {
                // Check if we should suggest parameters or amount
                boolean hasServerParam = false;
                boolean hasAmount = false;
                boolean hasItemsAdderFlag = false;

                for (int i = 2; i < args.length - 1; i++) {
                    if (args[i].equalsIgnoreCase("-server")) {
                        hasServerParam = true;
                        i++; // Skip server name
                    } else if (args[i].equalsIgnoreCase("-ia") || args[i].equalsIgnoreCase("-itemsadder")) {
                        hasItemsAdderFlag = true;
                    } else if (args[i].matches("\\d+")) {
                        hasAmount = true;
                    }
                }

                String partial = args[args.length - 1].toLowerCase();

                // Suggest flags and parameters if not already present
                if (!hasServerParam && "-server".startsWith(partial)) {
                    completions.add("-server");
                }

                if (!hasItemsAdderFlag) {
                    if ("-ia".startsWith(partial)) {
                        completions.add("-ia");
                    }
                    if ("-itemsadder".startsWith(partial)) {
                        completions.add("-itemsadder");
                    }
                }

                // Suggest amounts if not already present and not typing a flag
                if (!hasAmount && !partial.startsWith("-")) {
                    completions.addAll(Arrays.asList("1", "16", "32", "64"));
                }
            }
        }

        return completions;
    }
}
