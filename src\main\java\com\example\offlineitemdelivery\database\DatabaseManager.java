package com.example.offlineitemdelivery.database;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.utils.ValidationUtils;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.bukkit.configuration.ConfigurationSection;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.logging.Level;

public class DatabaseManager {
    
    private final OfflineItemDeliveryPlugin plugin;
    private HikariDataSource dataSource;
    
    // SQL Queries
    private static final String CREATE_TABLE_SQL =
        "CREATE TABLE IF NOT EXISTS offline_item_queue (" +
        "id INT AUTO_INCREMENT PRIMARY KEY, " +
        "player_uuid VARCHAR(36) NOT NULL, " +
        "player_name VARCHAR(16) NOT NULL, " +
        "server_id VARCHAR(50) NOT NULL, " +
        "item_data TEXT NOT NULL, " +
        "amount INT NOT NULL DEFAULT 1, " +
        "created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, " +
        "delivered BOOLEAN DEFAULT FALSE, " +
        "delivered_at TIMESTAMP NULL, " +
        "any_server BOOLEAN DEFAULT FALSE, " +
        "INDEX idx_player_uuid (player_uuid), " +
        "INDEX idx_server_id (server_id), " +
        "INDEX idx_delivered (delivered)" +
        ")";
    
    public DatabaseManager(OfflineItemDeliveryPlugin plugin) {
        this.plugin = plugin;
    }
    
    public void initialize() throws SQLException {
        setupDataSource();
        createTables();
        runMigrations();
        plugin.getLogger().info("Database initialized successfully");
    }
    
    private void setupDataSource() {
        ConfigurationSection dbConfig = plugin.getConfig().getConfigurationSection("database");
        if (dbConfig == null) {
            throw new IllegalStateException("Database configuration section not found in config.yml");
        }

        ConfigurationSection poolConfig = dbConfig.getConfigurationSection("pool");
        if (poolConfig == null) {
            throw new IllegalStateException("Database pool configuration section not found in config.yml");
        }

        HikariConfig config = new HikariConfig();

        // Database connection settings
        String host = dbConfig.getString("host", "localhost");
        int port = dbConfig.getInt("port", 3306);
        String database = dbConfig.getString("database", "minecraft");
        String username = dbConfig.getString("username", "minecraft");
        String password = dbConfig.getString("password", "password");

        // Validate database configuration
        if (!ValidationUtils.isValidDatabaseConfig(host, port, database, username)) {
            throw new IllegalArgumentException("Invalid database configuration parameters");
        }

        if (ValidationUtils.isNullOrEmpty(password)) {
            plugin.getLogger().warning("Database password is empty - this may cause connection issues");
        }

        try {
            config.setJdbcUrl("jdbc:mysql://" + host + ":" + port + "/" + database +
                             "?useSSL=false&allowPublicKeyRetrieval=true&serverTimezone=UTC&autoReconnect=true");
            config.setUsername(username);
            config.setPassword(password);
            config.setDriverClassName("com.mysql.cj.jdbc.Driver");
        } catch (Exception e) {
            throw new IllegalArgumentException("Failed to configure database connection", e);
        }
        
        // Connection pool settings
        config.setMaximumPoolSize(poolConfig.getInt("maximum-pool-size", 10));
        config.setMinimumIdle(poolConfig.getInt("minimum-idle", 2));
        config.setConnectionTimeout(poolConfig.getLong("connection-timeout", 30000));
        config.setIdleTimeout(poolConfig.getLong("idle-timeout", 600000));
        config.setMaxLifetime(poolConfig.getLong("max-lifetime", 1800000));
        
        // Additional settings
        config.setPoolName("OfflineItemDelivery-Pool");
        config.addDataSourceProperty("cachePrepStmts", "true");
        config.addDataSourceProperty("prepStmtCacheSize", "250");
        config.addDataSourceProperty("prepStmtCacheSqlLimit", "2048");
        
        dataSource = new HikariDataSource(config);
        
        plugin.getLogger().info("Database connection pool initialized");
    }
    
    private void createTables() throws SQLException {
        try (Connection connection = getConnection();
             PreparedStatement statement = connection.prepareStatement(CREATE_TABLE_SQL)) {
            
            statement.executeUpdate();
            plugin.getLogger().info("Database tables created/verified successfully");
            
        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to create database tables", e);
            throw e;
        }
    }

    /**
     * Runs database migrations to add missing columns
     */
    private void runMigrations() throws SQLException {
        try (Connection connection = getConnection()) {
            // Check if any_server column exists, if not add it
            if (!columnExists(connection, "offline_item_queue", "any_server")) {
                plugin.getLogger().info("Adding missing 'any_server' column to offline_item_queue table");
                try (PreparedStatement statement = connection.prepareStatement(
                    "ALTER TABLE offline_item_queue ADD COLUMN any_server BOOLEAN DEFAULT FALSE")) {
                    statement.executeUpdate();
                    plugin.getLogger().info("Successfully added 'any_server' column");
                }
            }

        } catch (SQLException e) {
            plugin.getLogger().log(Level.SEVERE, "Failed to run database migrations", e);
            throw e;
        }
    }

    /**
     * Checks if a column exists in a table
     */
    private boolean columnExists(Connection connection, String tableName, String columnName) throws SQLException {
        try (PreparedStatement statement = connection.prepareStatement(
            "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND COLUMN_NAME = ?")) {
            statement.setString(1, tableName);
            statement.setString(2, columnName);
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getInt(1) > 0;
                }
            }
        }
        return false;
    }

    public Connection getConnection() throws SQLException {
        if (dataSource == null) {
            throw new SQLException("DataSource is not initialized");
        }
        return dataSource.getConnection();
    }
    
    public void close() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            plugin.getLogger().info("Database connection pool closed");
        }
    }
    
    public boolean isConnected() {
        try (Connection connection = getConnection()) {
            return connection != null && !connection.isClosed();
        } catch (SQLException e) {
            return false;
        }
    }
}
