package com.example.offlineitemdelivery.database;

import com.example.offlineitemdelivery.models.PendingItem;
import com.example.offlineitemdelivery.utils.ItemSerializer;
import org.bukkit.inventory.ItemStack;

import java.sql.*;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

public class PendingItemDAO {
    
    private final DatabaseManager databaseManager;
    
    // SQL Queries
    private static final String INSERT_ITEM =
        "INSERT INTO offline_item_queue (player_uuid, player_name, server_id, item_data, amount, any_server) VALUES (?, ?, ?, ?, ?, ?)";
    
    private static final String SELECT_PENDING_ITEMS = 
        "SELECT * FROM offline_item_queue WHERE player_uuid = ? AND delivered = FALSE ORDER BY created_at ASC";
    
    private static final String SELECT_PENDING_ITEMS_BY_SERVER = 
        "SELECT * FROM offline_item_queue WHERE player_uuid = ? AND server_id = ? AND delivered = FALSE ORDER BY created_at ASC";
    
    private static final String MARK_DELIVERED = 
        "UPDATE offline_item_queue SET delivered = TRUE, delivered_at = CURRENT_TIMESTAMP WHERE id = ?";
    
    private static final String DELETE_ITEM = 
        "DELETE FROM offline_item_queue WHERE id = ?";
    
    private static final String COUNT_PENDING_ITEMS = 
        "SELECT COUNT(*) FROM offline_item_queue WHERE player_uuid = ? AND delivered = FALSE";
    
    private static final String SELECT_ALL_PENDING_BY_PLAYER = 
        "SELECT * FROM offline_item_queue WHERE player_uuid = ? ORDER BY created_at DESC";
    
    private static final String CLEAR_PENDING_ITEMS = 
        "DELETE FROM offline_item_queue WHERE player_uuid = ? AND delivered = FALSE";
    
    public PendingItemDAO(DatabaseManager databaseManager) {
        this.databaseManager = databaseManager;
    }
    
    public boolean addPendingItem(PendingItem item) throws SQLException {
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(INSERT_ITEM, Statement.RETURN_GENERATED_KEYS)) {
            
            statement.setString(1, item.getPlayerUuid().toString());
            statement.setString(2, item.getPlayerName());
            statement.setString(3, item.getServerId());
            statement.setString(4, ItemSerializer.serialize(item.getItemStack()));
            statement.setInt(5, item.getAmount());
            statement.setBoolean(6, item.isAnyServer());
            
            int affectedRows = statement.executeUpdate();
            
            if (affectedRows > 0) {
                try (ResultSet generatedKeys = statement.getGeneratedKeys()) {
                    if (generatedKeys.next()) {
                        item.setId(generatedKeys.getInt(1));
                    }
                }
                return true;
            }
            
            return false;
        }
    }
    
    public List<PendingItem> getPendingItems(UUID playerUuid) throws SQLException {
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(SELECT_PENDING_ITEMS)) {
            
            statement.setString(1, playerUuid.toString());
            
            try (ResultSet resultSet = statement.executeQuery()) {
                return mapResultSetToItems(resultSet);
            }
        }
    }
    
    public List<PendingItem> getPendingItemsByServer(UUID playerUuid, String serverId) throws SQLException {
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(SELECT_PENDING_ITEMS_BY_SERVER)) {
            
            statement.setString(1, playerUuid.toString());
            statement.setString(2, serverId);
            
            try (ResultSet resultSet = statement.executeQuery()) {
                return mapResultSetToItems(resultSet);
            }
        }
    }
    
    public boolean markAsDelivered(int itemId) throws SQLException {
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(MARK_DELIVERED)) {
            
            statement.setInt(1, itemId);
            return statement.executeUpdate() > 0;
        }
    }
    
    public boolean deleteItem(int itemId) throws SQLException {
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(DELETE_ITEM)) {
            
            statement.setInt(1, itemId);
            return statement.executeUpdate() > 0;
        }
    }
    
    public int countPendingItems(UUID playerUuid) throws SQLException {
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(COUNT_PENDING_ITEMS)) {
            
            statement.setString(1, playerUuid.toString());
            
            try (ResultSet resultSet = statement.executeQuery()) {
                if (resultSet.next()) {
                    return resultSet.getInt(1);
                }
            }
        }
        return 0;
    }
    
    public List<PendingItem> getAllItemsByPlayer(UUID playerUuid) throws SQLException {
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(SELECT_ALL_PENDING_BY_PLAYER)) {
            
            statement.setString(1, playerUuid.toString());
            
            try (ResultSet resultSet = statement.executeQuery()) {
                return mapResultSetToItems(resultSet);
            }
        }
    }
    
    public boolean clearPendingItems(UUID playerUuid) throws SQLException {
        try (Connection connection = databaseManager.getConnection();
             PreparedStatement statement = connection.prepareStatement(CLEAR_PENDING_ITEMS)) {
            
            statement.setString(1, playerUuid.toString());
            return statement.executeUpdate() > 0;
        }
    }
    
    private List<PendingItem> mapResultSetToItems(ResultSet resultSet) throws SQLException {
        List<PendingItem> items = new ArrayList<>();

        while (resultSet.next()) {
            try {
                PendingItem item = new PendingItem();
                item.setId(resultSet.getInt("id"));
                item.setPlayerUuid(UUID.fromString(resultSet.getString("player_uuid")));
                item.setPlayerName(resultSet.getString("player_name"));
                item.setServerId(resultSet.getString("server_id"));

                // Deserialize item data with error handling
                String itemData = resultSet.getString("item_data");
                try {
                    ItemStack itemStack = ItemSerializer.deserialize(itemData);
                    item.setItemStack(itemStack);
                } catch (Exception e) {
                    // If deserialization fails, create a placeholder ItemStack
                    System.err.println("Failed to deserialize item data for ID " + resultSet.getInt("id") + ": " + e.getMessage());
                    // Set a null ItemStack - this will be handled by the display methods
                    item.setItemStack(null);
                }

                item.setAmount(resultSet.getInt("amount"));
                item.setCreatedAt(resultSet.getTimestamp("created_at"));
                item.setDelivered(resultSet.getBoolean("delivered"));
                item.setDeliveredAt(resultSet.getTimestamp("delivered_at"));
                item.setAnyServer(resultSet.getBoolean("any_server"));

                items.add(item);
            } catch (Exception e) {
                // Log the error but continue processing other items
                System.err.println("Failed to process pending item from database: " + e.getMessage());
            }
        }

        return items;
    }
}
