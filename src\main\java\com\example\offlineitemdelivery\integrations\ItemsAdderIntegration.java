package com.example.offlineitemdelivery.integrations;

import dev.lone.itemsadder.api.CustomStack;
import org.bukkit.Bukkit;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.List;

public class ItemsAdderIntegration {
    
    private static boolean itemsAdderEnabled = false;
    
    /**
     * Initialize ItemsAdder integration
     */
    public static void initialize() {
        if (Bukkit.getPluginManager().getPlugin("ItemsAdder") != null) {
            itemsAdderEnabled = true;
            Bukkit.getLogger().info("[OfflineItemDelivery] ItemsAdder integration enabled!");
        } else {
            itemsAdderEnabled = false;
            Bukkit.getLogger().info("[OfflineItemDelivery] ItemsAdder not found, integration disabled.");
        }
    }
    
    /**
     * Check if ItemsAdder is available
     */
    public static boolean isEnabled() {
        return itemsAdderEnabled;
    }
    
    /**
     * Get an ItemsAdder custom item by its namespace ID
     * @param itemId The ItemsAdder item ID (e.g., "myitems:custom_sword")
     * @return ItemStack if found, null otherwise
     */
    public static ItemStack getCustomItem(String itemId) {
        if (!itemsAdderEnabled) {
            return null;
        }
        
        try {
            CustomStack customStack = CustomStack.getInstance(itemId);
            if (customStack != null) {
                return customStack.getItemStack();
            }
        } catch (Exception e) {
            Bukkit.getLogger().warning("[OfflineItemDelivery] Failed to get ItemsAdder item '" + itemId + "': " + e.getMessage());
        }
        
        return null;
    }
    
    /**
     * Check if an item ID exists in ItemsAdder
     * @param itemId The ItemsAdder item ID to check
     * @return true if the item exists, false otherwise
     */
    public static boolean itemExists(String itemId) {
        if (!itemsAdderEnabled) {
            return false;
        }
        
        try {
            CustomStack customStack = CustomStack.getInstance(itemId);
            return customStack != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get all available ItemsAdder item IDs for tab completion
     * @return List of ItemsAdder item IDs
     */
    public static List<String> getAllCustomItemIds() {
        List<String> itemIds = new ArrayList<>();
        
        if (!itemsAdderEnabled) {
            return itemIds;
        }
        
        try {
            // Get all registered custom items
            for (String itemId : CustomStack.getNamespacedIdsInRegistry()) {
                itemIds.add(itemId);
            }
        } catch (Exception e) {
            Bukkit.getLogger().warning("[OfflineItemDelivery] Failed to get ItemsAdder item list: " + e.getMessage());
        }
        
        return itemIds;
    }
    
    /**
     * Get ItemsAdder item IDs that start with a specific prefix (for tab completion)
     * @param prefix The prefix to match
     * @return List of matching ItemsAdder item IDs
     */
    public static List<String> getCustomItemIds(String prefix) {
        List<String> matchingIds = new ArrayList<>();
        
        if (!itemsAdderEnabled) {
            return matchingIds;
        }
        
        String lowerPrefix = prefix.toLowerCase();
        
        try {
            for (String itemId : CustomStack.getNamespacedIdsInRegistry()) {
                if (itemId.toLowerCase().startsWith(lowerPrefix)) {
                    matchingIds.add(itemId);
                }
            }
        } catch (Exception e) {
            Bukkit.getLogger().warning("[OfflineItemDelivery] Failed to get ItemsAdder item list for prefix '" + prefix + "': " + e.getMessage());
        }
        
        return matchingIds;
    }
    
    /**
     * Get the display name of an ItemsAdder item
     * @param itemId The ItemsAdder item ID
     * @return Display name if found, the item ID otherwise
     */
    public static String getItemDisplayName(String itemId) {
        if (!itemsAdderEnabled) {
            return itemId;
        }
        
        try {
            CustomStack customStack = CustomStack.getInstance(itemId);
            if (customStack != null) {
                ItemStack itemStack = customStack.getItemStack();
                if (itemStack != null && itemStack.hasItemMeta() && itemStack.getItemMeta().hasDisplayName()) {
                    return itemStack.getItemMeta().getDisplayName();
                }
            }
        } catch (Exception e) {
            // Fall through to return itemId
        }
        
        return itemId;
    }
    
    /**
     * Check if an ItemStack is a custom ItemsAdder item
     * @param itemStack The ItemStack to check
     * @return true if it's a custom ItemsAdder item, false otherwise
     */
    public static boolean isCustomItem(ItemStack itemStack) {
        if (!itemsAdderEnabled || itemStack == null) {
            return false;
        }
        
        try {
            CustomStack customStack = CustomStack.byItemStack(itemStack);
            return customStack != null;
        } catch (Exception e) {
            return false;
        }
    }
    
    /**
     * Get the ItemsAdder ID of a custom ItemStack
     * @param itemStack The ItemStack to get the ID for
     * @return The ItemsAdder ID if it's a custom item, null otherwise
     */
    public static String getCustomItemId(ItemStack itemStack) {
        if (!itemsAdderEnabled || itemStack == null) {
            return null;
        }
        
        try {
            CustomStack customStack = CustomStack.byItemStack(itemStack);
            if (customStack != null) {
                return customStack.getNamespacedID();
            }
        } catch (Exception e) {
            // Fall through to return null
        }
        
        return null;
    }
}
