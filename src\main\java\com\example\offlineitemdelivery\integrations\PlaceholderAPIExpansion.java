package com.example.offlineitemdelivery.integrations;

import com.example.offlineitemdelivery.OfflineItemDeliveryPlugin;
import com.example.offlineitemdelivery.services.ItemDeliveryService;
import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import org.bukkit.OfflinePlayer;
import org.jetbrains.annotations.NotNull;

import java.sql.SQLException;

/**
 * PlaceholderAPI expansion for OfflineItemDelivery plugin
 * Provides placeholders for pending item counts and related information
 */
public class PlaceholderAPIExpansion extends PlaceholderExpansion {

    private final OfflineItemDeliveryPlugin plugin;
    private final ItemDeliveryService itemDeliveryService;

    public PlaceholderAPIExpansion(OfflineItemDeliveryPlugin plugin, ItemDeliveryService itemDeliveryService) {
        this.plugin = plugin;
        this.itemDeliveryService = itemDeliveryService;
    }

    @Override
    public @NotNull String getIdentifier() {
        return "offlineitemdelivery";
    }

    @Override
    public @NotNull String getAuthor() {
        return plugin.getDescription().getAuthors().toString();
    }

    @Override
    public @NotNull String getVersion() {
        return plugin.getDescription().getVersion();
    }

    @Override
    public boolean persist() {
        return true; // This expansion should persist through PlaceholderAPI reloads
    }

    @Override
    public String onRequest(OfflinePlayer player, @NotNull String params) {
        if (player == null) {
            return "";
        }

        // Handle different placeholder requests
        switch (params.toLowerCase()) {
            case "pending_count":
            case "total_claimable":
                return getPendingItemCount(player);
            
            case "has_pending":
                return hasPendingItems(player);
            
            case "max_pending":
                return String.valueOf(plugin.getConfig().getInt("settings.max-pending-items", 50));
            
            case "pending_percentage":
                return getPendingPercentage(player);
            
            default:
                return null; // Placeholder not found
        }
    }

    /**
     * Get the number of pending items for a player
     * @param player The player to check
     * @return String representation of pending item count, or "0" if error
     */
    private String getPendingItemCount(OfflinePlayer player) {
        try {
            int count = itemDeliveryService.getPendingItemCount(player.getUniqueId());
            return String.valueOf(count);
        } catch (SQLException e) {
            plugin.getLogger().warning("Failed to get pending item count for player " + player.getName() + ": " + e.getMessage());
            return "0";
        }
    }

    /**
     * Check if player has any pending items
     * @param player The player to check
     * @return "true" if player has pending items, "false" otherwise
     */
    private String hasPendingItems(OfflinePlayer player) {
        try {
            int count = itemDeliveryService.getPendingItemCount(player.getUniqueId());
            return count > 0 ? "true" : "false";
        } catch (SQLException e) {
            plugin.getLogger().warning("Failed to check pending items for player " + player.getName() + ": " + e.getMessage());
            return "false";
        }
    }

    /**
     * Get the percentage of pending items vs max allowed
     * @param player The player to check
     * @return String representation of percentage (0-100)
     */
    private String getPendingPercentage(OfflinePlayer player) {
        try {
            int count = itemDeliveryService.getPendingItemCount(player.getUniqueId());
            int maxItems = plugin.getConfig().getInt("settings.max-pending-items", 50);
            
            if (maxItems <= 0) {
                return "0";
            }
            
            double percentage = ((double) count / maxItems) * 100;
            return String.valueOf(Math.round(percentage));
        } catch (SQLException e) {
            plugin.getLogger().warning("Failed to calculate pending percentage for player " + player.getName() + ": " + e.getMessage());
            return "0";
        }
    }
}
