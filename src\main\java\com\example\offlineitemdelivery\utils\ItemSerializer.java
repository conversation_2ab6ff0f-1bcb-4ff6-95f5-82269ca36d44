package com.example.offlineitemdelivery.utils;

import org.bukkit.inventory.ItemStack;
import org.bukkit.util.io.BukkitObjectInputStream;
import org.bukkit.util.io.BukkitObjectOutputStream;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.Base64;

public class ItemSerializer {
    
    /**
     * Serializes an ItemStack to a Base64 encoded string
     * @param itemStack The ItemStack to serialize
     * @return Base64 encoded string representation of the ItemStack
     * @throws IllegalArgumentException if serialization fails
     */
    public static String serialize(ItemStack itemStack) {
        if (itemStack == null) {
            return "";
        }
        
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BukkitObjectOutputStream dataOutput = new BukkitObjectOutputStream(outputStream);
            
            dataOutput.writeObject(itemStack);
            dataOutput.close();
            
            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (IOException e) {
            throw new IllegalArgumentException("Failed to serialize ItemStack", e);
        }
    }
    
    /**
     * Deserializes a Base64 encoded string back to an ItemStack
     * @param data Base64 encoded string representation of an ItemStack
     * @return The deserialized ItemStack, or null if data is empty or invalid
     * @throws IllegalArgumentException if deserialization fails
     */
    public static ItemStack deserialize(String data) {
        if (data == null || data.isEmpty()) {
            return null;
        }

        try {
            byte[] bytes = Base64.getDecoder().decode(data);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            BukkitObjectInputStream dataInput = new BukkitObjectInputStream(inputStream);

            Object obj = dataInput.readObject();
            dataInput.close();

            // Validate that the deserialized object is actually an ItemStack
            if (!(obj instanceof ItemStack)) {
                throw new IllegalArgumentException("Deserialized object is not an ItemStack: " + obj.getClass().getName());
            }

            ItemStack itemStack = (ItemStack) obj;

            // Additional validation to ensure the ItemStack is valid
            if (itemStack.getType() == null) {
                throw new IllegalArgumentException("Deserialized ItemStack has null type");
            }

            return itemStack;
        } catch (IOException | ClassNotFoundException e) {
            throw new IllegalArgumentException("Failed to deserialize ItemStack from data: " + data.substring(0, Math.min(50, data.length())) + "...", e);
        } catch (IllegalArgumentException e) {
            // Re-throw our custom validation errors
            throw e;
        } catch (Exception e) {
            throw new IllegalArgumentException("Unexpected error during ItemStack deserialization", e);
        }
    }
    
    /**
     * Serializes an array of ItemStacks to a Base64 encoded string
     * @param items The ItemStack array to serialize
     * @return Base64 encoded string representation of the ItemStack array
     * @throws IllegalArgumentException if serialization fails
     */
    public static String serializeArray(ItemStack[] items) {
        if (items == null) {
            return "";
        }
        
        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            BukkitObjectOutputStream dataOutput = new BukkitObjectOutputStream(outputStream);
            
            dataOutput.writeObject(items);
            dataOutput.close();
            
            return Base64.getEncoder().encodeToString(outputStream.toByteArray());
        } catch (IOException e) {
            throw new IllegalArgumentException("Failed to serialize ItemStack array", e);
        }
    }
    
    /**
     * Deserializes a Base64 encoded string back to an ItemStack array
     * @param data Base64 encoded string representation of an ItemStack array
     * @return The deserialized ItemStack array, or null if data is empty
     * @throws IllegalArgumentException if deserialization fails
     */
    public static ItemStack[] deserializeArray(String data) {
        if (data == null || data.isEmpty()) {
            return null;
        }
        
        try {
            byte[] bytes = Base64.getDecoder().decode(data);
            ByteArrayInputStream inputStream = new ByteArrayInputStream(bytes);
            BukkitObjectInputStream dataInput = new BukkitObjectInputStream(inputStream);
            
            ItemStack[] items = (ItemStack[]) dataInput.readObject();
            dataInput.close();
            
            return items;
        } catch (IOException | ClassNotFoundException e) {
            throw new IllegalArgumentException("Failed to deserialize ItemStack array from data: " + data, e);
        }
    }
}
