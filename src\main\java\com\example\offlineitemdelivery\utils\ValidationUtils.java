package com.example.offlineitemdelivery.utils;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.UUID;

public class ValidationUtils {
    
    /**
     * Validates if a UUID string is valid
     */
    public static boolean isValidUUID(String uuidString) {
        if (uuidString == null || uuidString.isEmpty()) {
            return false;
        }
        
        try {
            UUID.fromString(uuidString);
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
    
    /**
     * Validates if a player name is valid
     */
    public static boolean isValidPlayerName(String playerName) {
        if (playerName == null || playerName.isEmpty()) {
            return false;
        }
        
        // Minecraft player names are 3-16 characters, alphanumeric and underscore only
        return playerName.matches("^[a-zA-Z0-9_]{3,16}$");
    }
    
    /**
     * Validates if an ItemStack is valid for storage
     */
    public static boolean isValidItemStack(ItemStack itemStack) {
        if (itemStack == null) {
            return false;
        }
        
        // Check if material is valid and not air
        Material material = itemStack.getType();
        if (material == null || material == Material.AIR) {
            return false;
        }
        
        // Check if it's a valid item (not a block-only material)
        if (!material.isItem()) {
            return false;
        }
        
        // Check amount is reasonable
        int amount = itemStack.getAmount();
        if (amount <= 0 || amount > material.getMaxStackSize() * 64) { // Max 64 stacks
            return false;
        }
        
        return true;
    }
    
    /**
     * Validates if a server ID is valid
     */
    public static boolean isValidServerId(String serverId) {
        if (serverId == null || serverId.isEmpty()) {
            return false;
        }
        
        // Server ID should be alphanumeric, underscore, hyphen, and reasonable length
        return serverId.matches("^[a-zA-Z0-9_-]{1,50}$");
    }
    
    /**
     * Validates if an amount is reasonable
     */
    public static boolean isValidAmount(int amount) {
        return amount > 0 && amount <= 2304; // 36 stacks of 64
    }
    
    /**
     * Sanitizes a player name for database storage
     */
    public static String sanitizePlayerName(String playerName) {
        if (playerName == null) {
            return "";
        }
        
        // Remove any non-alphanumeric characters except underscore
        return playerName.replaceAll("[^a-zA-Z0-9_]", "").substring(0, Math.min(playerName.length(), 16));
    }
    
    /**
     * Sanitizes a server ID for database storage
     */
    public static String sanitizeServerId(String serverId) {
        if (serverId == null) {
            return "default";
        }
        
        // Remove any invalid characters
        String sanitized = serverId.replaceAll("[^a-zA-Z0-9_-]", "");
        
        // Ensure it's not empty and not too long
        if (sanitized.isEmpty()) {
            return "default";
        }
        
        return sanitized.substring(0, Math.min(sanitized.length(), 50));
    }
    
    /**
     * Checks if a string is null or empty
     */
    public static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
    
    /**
     * Validates database connection parameters
     */
    public static boolean isValidDatabaseConfig(String host, int port, String database, String username) {
        if (isNullOrEmpty(host) || isNullOrEmpty(database) || isNullOrEmpty(username)) {
            return false;
        }
        
        if (port <= 0 || port > 65535) {
            return false;
        }
        
        // Basic hostname validation
        if (!host.matches("^[a-zA-Z0-9.-]+$")) {
            return false;
        }
        
        return true;
    }
}
