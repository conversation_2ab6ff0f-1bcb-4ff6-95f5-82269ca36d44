name: OfflineItemDelivery
version: 1.0.0
main: com.example.offlineitemdelivery.OfflineItemDeliveryPlugin
api-version: 1.20
author: YourName
description: A plugin for delivering items to offline players with MySQL persistence

softdepend: [ItemsAdder, PlaceholderAPI]

commands:
  giveoffline:
    description: Give an item to an offline player
    usage: /giveoffline <player> <item> [amount] [-server <servername>] [-ia]
    permission: offlineitemdelivery.give
    aliases: [giveoff, offlinegive]

  offlinequeue:
    description: Manage the offline item delivery queue
    usage: /offlinequeue <list|clear|remove> [player]
    permission: offlineitemdelivery.admin
    aliases: [oqueue, itemqueue]

  claimitems:
    description: Open the claim items GUI
    usage: /claimitems
    permission: offlineitemdelivery.claim
    aliases: [claim, getitems]

permissions:
  offlineitemdelivery.give:
    description: Allows giving items to offline players
    default: op

  offlineitemdelivery.admin:
    description: Allows managing the offline item queue
    default: op

  offlineitemdelivery.claim:
    description: Allows claiming pending items
    default: true

  offlineitemdelivery.*:
    description: Gives access to all OfflineItemDelivery permissions
    default: op
    children:
      offlineitemdelivery.give: true
      offlineitemdelivery.admin: true
      offlineitemdelivery.claim: true
