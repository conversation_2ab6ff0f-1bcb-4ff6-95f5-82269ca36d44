C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\commands\OfflineQueueCommand.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\listeners\PlayerJoinListener.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\integrations\PlaceholderAPIExpansion.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\database\DatabaseManager.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\gui\ClaimItemsGUI.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\commands\ClaimItemsCommand.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\OfflineItemDeliveryPlugin.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\models\PendingItem.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\database\PendingItemDAO.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\services\ItemDeliveryService.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\integrations\ItemsAdderIntegration.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\utils\ItemSerializer.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\commands\GiveOfflineCommand.java
C:\Users\<USER>\OneDrive\Desktop\code\src\main\java\com\example\offlineitemdelivery\utils\ValidationUtils.java
